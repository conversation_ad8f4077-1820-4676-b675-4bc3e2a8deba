<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>黑名单测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>文件黑名单测试</h1>
    <div id="result"></div>
    
    <script>
        // 测试获取配置数据
        axios.get('http://localhost:5005/data')
            .then(response => {
                console.log('Response:', response.data);
                const data = response.data.data;
                if (data && data.file_blacklist) {
                    document.getElementById('result').innerHTML = 
                        '<h2>黑名单项目 (' + data.file_blacklist.length + ' 项):</h2>' +
                        '<ul>' + 
                        data.file_blacklist.map(item => '<li>' + item + '</li>').join('') +
                        '</ul>';
                } else {
                    document.getElementById('result').innerHTML = '<p>未找到黑名单配置或需要登录</p>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = '<p>错误: ' + error.message + '</p>';
            });
    </script>
</body>
</html>
